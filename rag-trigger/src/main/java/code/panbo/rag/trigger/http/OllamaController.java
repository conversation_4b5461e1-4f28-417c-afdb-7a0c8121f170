package code.panbo.rag.trigger.http;

import code.panbo.rag.api.IAiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/ollama")
@CrossOrigin("*")
@Slf4j
public class OllamaController implements IAiService {

    @Resource
    private OllamaChatModel ollamaChatModel;
    @Resource
    private PgVectorStore pgVectorStore;

    /**
     * 创建一个简单的对话示例
     * 请求接口示例：http://localhost:8090/api/v1/ollama/generate?model=deepseek-r1:1.5b&message=1+1
     *
     * @param model   模型
     * @param message 消息
     * @return
     */
    @GetMapping("/generate")
    @Override
    public ChatResponse generate(@RequestParam("model") String model, @RequestParam("message") String message) {
        log.info("Ollama 简单对话请求 - 模型: {}, 消息: {}", model, message);
        return ollamaChatModel.call(
                new Prompt(message, OllamaOptions.builder().model(model).build())
        );
    }

    /**
     * 创建一个流式对话示例
     *
     * @param model   模型
     * @param message 消息
     * @return Flux<ChatResponse>
     */
    @GetMapping(value = "/generate_stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Override
    public Flux<ChatResponse> generateStream(@RequestParam("model") String model, @RequestParam("message") String message) {
        log.info("Ollama 流式对话请求 - 模型: {}, 消息: {}", model, message);

        try {
            Flux<ChatResponse> responseFlux = ollamaChatModel.stream(
                            new Prompt(message, OllamaOptions.builder().model(model).build())
                    )
                    .onErrorResume(e -> {
                        log.error("Ollama 流式对话请求异常", e);
                        return Flux.empty();
                    })
                    .timeout(Duration.ofSeconds(60), Flux.empty()); // 设置超时
            return responseFlux
                    .doOnCancel(() -> log.info("客户端取消了请求连接"))
                    .doOnError(e -> log.error("流处理过程中发生错误", e))
                    .doOnComplete(() -> log.info("流式对话完成"));
        } catch (Exception e) {
            log.error("创建流式对话时发生异常", e);
            return Flux.error(e);
        }
    }

    /**
     * 创建一个流式对话示例，并使用RAG标签进行文档搜索
     *
     * @param model   模型
     * @param ragTag  RAG标签
     * @param message 消息
     * @return Flux<ChatResponse>
     */
    @GetMapping(value = "/generate_stream_rag", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Override
    public Flux<ChatResponse> generateStreamRag(@RequestParam String model, @RequestParam String ragTag, @RequestParam String message) {
        log.info("Ollama RAG流式对话请求 - 模型: {}, RAG标签: {}, 消息: {}", model, ragTag, message);

        try {
            // 设置默认的系统提示词
            String SYSTEM_PROMPT = """
                    Use the information from the DOCUMENTS section to provide accurate answers but act as if you knew this information innately.
                    If unsure, simply state that you don't know.
                    Another thing you need to note is that your reply must be in Chinese!
                    DOCUMENTS:
                        {documents}
                    """;

            // 指定文档搜索
            SearchRequest request = SearchRequest
                    .builder()
                    .query(message)
                    .topK(5)
                    .filterExpression("knowledge == '" + ragTag + "'")
                    .build();

            // 使用pgVectorStore进行相似度搜索，获取相关文档列表
            List<Document> documents = pgVectorStore.similaritySearch(request);
            if (documents != null) {
                log.info("找到 {} 个相关文档", documents.size());
            }

            // 将文档列表中的内容收集并拼接成一个字符串
            String documentCollectors = documents.stream()
                    .map(Document::getText).collect(Collectors.joining());

            // 创建一个系统提示消息，包含收集到的文档内容
            Message ragMessage =
                    new SystemPromptTemplate(SYSTEM_PROMPT)
                            .createMessage(Map.of("documents", documentCollectors));

            // 初始化消息列表，添加用户消息和系统提示消息
            List<Message> messages = new ArrayList<>();
            messages.add(new UserMessage(message));
            messages.add(ragMessage);

            // 调用chatClient进行对话请求，传入消息列表和OllamaOptions配置
            Flux<ChatResponse> responseFlux = ollamaChatModel.stream(
                            new Prompt(messages, OllamaOptions.builder().model(model).build())
                    )
                    .onErrorResume(e -> {
                        log.error("Ollama RAG流式对话请求异常", e);
                        return Flux.empty();
                    })
                    .timeout(Duration.ofSeconds(120), Flux.empty()); // RAG查询可能更长，给2分钟超时

            return responseFlux
                    .doOnCancel(() -> log.info("客户端取消了RAG请求连接"))
                    .doOnError(e -> log.error("RAG流处理过程中发生错误", e))
                    .doOnComplete(() -> log.info("RAG流式对话完成"));

        } catch (Exception e) {
            log.error("创建RAG流式对话时发生异常", e);
            return Flux.error(e);
        }
    }
}
