import React, { createContext, useContext, useMemo, useState, useEffect } from 'react'
import type { AppConfig, ApiType } from '../types'
import { storageService } from '@/services'
import { DEFAULT_CONFIG } from '@/constants'

interface ConfigContextValue {
  config: AppConfig
  updateConfig: (updates: Partial<AppConfig>) => void
  resetConfig: () => void
}

const ConfigContext = createContext<ConfigContextValue | undefined>(undefined)

export function ConfigProvider({ children }: { children: React.ReactNode }) {
  const [config, setConfig] = useState<AppConfig>(() => storageService.getConfig())

  useEffect(() => {
    // 初始化时若没有存储，则写入默认配置
    if (!config) {
      storageService.saveConfig(DEFAULT_CONFIG)
      setConfig(DEFAULT_CONFIG)
    }
  }, [])

  const updateConfig = (updates: Partial<AppConfig>) => {
    setConfig(prev => {
      const next = { ...prev, ...updates }
      storageService.saveConfig(next)
      return next
    })
  }

  const resetConfig = () => {
    storageService.saveConfig(DEFAULT_CONFIG)
    setConfig(DEFAULT_CONFIG)
  }

  const value = useMemo(() => ({ config, updateConfig, resetConfig }), [config])

  return <ConfigContext.Provider value={value}>{children}</ConfigContext.Provider>
}

export function useConfig() {
  const ctx = useContext(ConfigContext)
  if (!ctx) throw new Error('useConfig must be used within ConfigProvider')
  return ctx
}
