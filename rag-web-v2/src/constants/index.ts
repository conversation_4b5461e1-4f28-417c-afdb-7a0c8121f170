import type { ApiConfig } from '../types'

// 允许通过环境变量覆盖后端 API 基址（例如：VITE_API_BASE_URL=http://localhost:8090/api/v1）
const API_BASE_URL: string = (import.meta as any)?.env?.VITE_API_BASE_URL || "/api/v1"

// API 配置
export const API_CONFIG: ApiConfig = {
  BASE_URL: API_BASE_URL,
  ENDPOINTS: {
    OLLAMA: {
      CHAT: "/ollama/generate_stream",
      CHAT_RAG: "/ollama/generate_stream_rag",
      GENERATE: "/ollama/generate",
    },
    OPENAI: {
      CHAT: "/openai/generate_stream",
      CHAT_RAG: "/openai/generate_stream_rag",
      GENERATE: "/openai/generate",
    },
    RAG: {
      UPLOAD: "/rag/file/upload",
      TAGS: "/rag/tags",
      GIT_UPLOAD: "/rag/git-repo/upload"
    },
    MODEL: {
      LIST: "/chat/models",
    }
  },
  MODELS: {
    OLLAMA: ["qwen3", "gemma3"],
    OPENAI: ["Qwen/Qwen2.5-7B-Instruct", "moonshotai/Kimi-K2-Instruct"]
  }
}

// 默认配置
export const DEFAULT_CONFIG = {
  apiType: 'OLLAMA' as const,
  selectedModel: 'qwen3',
  selectedRagTag: '',
  theme: 'light' as const,
}

// 本地存储键名
export const STORAGE_KEYS = {
  CHATS: 'rag-web-chats',
  CONFIG: 'rag-web-config',
  THEME: 'rag-web-theme',
}

// 支持的文件类型
export const SUPPORTED_FILE_TYPES = [
  '.md', '.txt', '.sql', '.json', '.csv',
  '.html', '.js', '.java', '.py', '.xml'
]

// 最大文件大小 (10MB)
export const MAX_FILE_SIZE = 10 * 1024 * 1024

// 最大消息长度
export const MAX_MESSAGE_LENGTH = 4096

// 默认消息
export const DEFAULT_MESSAGES = {
  WELCOME: '开始与AI对话...',
  ERROR: '抱歉，发生了错误，请稍后重试。',
  NETWORK_ERROR: '网络连接错误，请检查网络设置。',
  FILE_TOO_LARGE: `文件大小不能超过 ${MAX_FILE_SIZE / 1024 / 1024}MB`,
  UNSUPPORTED_FILE: `不支持的文件类型，支持的类型：${SUPPORTED_FILE_TYPES.join(', ')}`,
}

// 动画持续时间
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
}

// 主题相关
export const THEME_STORAGE_KEY = 'rag-web-theme'
