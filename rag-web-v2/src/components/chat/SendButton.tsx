import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import {
  Send,
  Square,
  Loader2,
} from 'lucide-react'

interface SendButtonProps {
  onSend?: () => void
  onStop?: () => void
  disabled?: boolean
  isLoading?: boolean
  canSend?: boolean
  size?: 'sm' | 'default' | 'lg' | 'icon'
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  className?: string
}

export default function SendButton({
  onSend,
  onStop,
  disabled = false,
  isLoading = false,
  canSend = true,
  size = 'icon',
  variant = 'default',
  className,
}: SendButtonProps) {
  const handleClick = () => {
    if (isLoading && onStop) {
      onStop()
    } else if (canSend && onSend) {
      onSend()
    }
  }

  if (isLoading) {
    return (
      <Button
        variant="outline"
        size={size}
        onClick={handleClick}
        className={cn(
          "text-destructive border-destructive hover:bg-destructive/10",
          className
        )}
        disabled={disabled}
      >
        <Square className="w-4 h-4" />
        {size !== 'icon' && <span className="ml-2">停止</span>}
      </Button>
    )
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={disabled || !canSend}
      className={className}
    >
      <Send className="w-4 h-4" />
      {size !== 'icon' && <span className="ml-2">发送</span>}
    </Button>
  )
}

// 带加载状态的发送按钮变体
export function LoadingSendButton({
  isLoading = false,
  loadingText = "发送中...",
  ...props
}: SendButtonProps & {
  loadingText?: string
}) {
  return (
    <Button
      {...props}
      disabled={props.disabled || isLoading}
    >
      {isLoading ? (
        <>
          <Loader2 className="w-4 h-4 animate-spin" />
          {props.size !== 'icon' && <span className="ml-2">{loadingText}</span>}
        </>
      ) : (
        <>
          <Send className="w-4 h-4" />
          {props.size !== 'icon' && <span className="ml-2">发送</span>}
        </>
      )}
    </Button>
  )
}
