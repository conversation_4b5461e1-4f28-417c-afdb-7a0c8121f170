import { useState, useCallback, useRef } from 'react'
import type { Message as MessageType } from '../../types'
import { useConfig } from '@/contexts/ConfigContext'
import { streamService, API_CONFIG } from '@/services'
import ChatContainer from './ChatContainer'
import MessageInput from './MessageInput'

interface ChatInterfaceProps {
  onFileUpload?: (files: FileList) => void
}

export default function ChatInterface({
  onFileUpload,
}: ChatInterfaceProps) {
  const { config } = useConfig()
  const [messages, setMessages] = useState<MessageType[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const streamControllerRef = useRef<{ abort: () => void } | null>(null)

  // 添加消息
  const addMessage = useCallback((message: Omit<MessageType, 'id' | 'timestamp'>) => {
    const timestamp = Date.now()
    const newMessage: MessageType = {
      ...message,
      id: `${timestamp}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp,
    }
    setMessages(prev => [...prev, newMessage])
    return newMessage
  }, [])

  // 更新最后一条消息
  const updateLastMessage = useCallback((content: string) => {
    setMessages(prev => {
      const updated = [...prev]
      if (updated.length > 0 && updated[updated.length - 1].role === 'assistant') {
        updated[updated.length - 1] = {
          ...updated[updated.length - 1],
          content,
        }
      }
      return updated
    })
  }, [])

  // 处理发送消息
  const handleSendMessage = useCallback(async (content: string) => {
    // 添加用户消息
    addMessage({
      role: 'user',
      content,
    })

    // 添加空的助手消息用于流式更新
    const assistantMessage = addMessage({
      role: 'assistant',
      content: '',
      isStreaming: true,
    })

    setIsLoading(true)

    // 构建 SSE URL
    const endpoint = config.selectedRagTag
      ? API_CONFIG.ENDPOINTS[config.apiType].CHAT_RAG
      : API_CONFIG.ENDPOINTS[config.apiType].CHAT

    const params = new URLSearchParams({
      model: config.selectedModel,
      message: content,
    })

    if (config.selectedRagTag) {
      params.append('ragTag', config.selectedRagTag)
    }

    const url = `${API_CONFIG.BASE_URL}${endpoint}?${params.toString()}`

    let accumulatedContent = ''

    // 创建流式连接
    const controller = streamService.createStream(url, {
      onMessage: (chunk: string) => {
        accumulatedContent += chunk
        updateLastMessage(accumulatedContent)
      },
      onComplete: () => {
        setIsLoading(false)
        streamControllerRef.current = null
        // 移除 isStreaming 标记
        setMessages(prev => {
          const updated = [...prev]
          if (updated.length > 0 && updated[updated.length - 1].role === 'assistant') {
            updated[updated.length - 1] = {
              ...updated[updated.length - 1],
              isStreaming: false,
            }
          }
          return updated
        })
      },
      onError: (error) => {
        console.error('Stream error:', error)
        setIsLoading(false)
        streamControllerRef.current = null
        updateLastMessage('抱歉，发生了错误，请稍后重试。')
      },
    })

    streamControllerRef.current = controller
  }, [config, addMessage, updateLastMessage])

  // 停止生成
  const handleStopGeneration = useCallback(() => {
    if (streamControllerRef.current) {
      streamControllerRef.current.abort()
      streamControllerRef.current = null
      setIsLoading(false)
    }
  }, [])

  // 处理复制消息
  const handleCopyMessage = useCallback(async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      // 这里可以添加成功提示
    } catch (error) {
      console.error('复制失败:', error)
    }
  }, [])

  // 处理重新生成消息
  const handleRegenerateMessage = useCallback((messageId: string) => {
    // 找到要重新生成的消息
    const messageIndex = messages.findIndex(msg => msg.id === messageId)
    if (messageIndex === -1) return

    // 获取该消息之前的最后一个用户消息
    const userMessages = messages.slice(0, messageIndex).filter(msg => msg.role === 'user')
    const lastUserMessage = userMessages[userMessages.length - 1]

    if (lastUserMessage) {
      // 删除从该 AI 消息开始的所有后续消息
      setMessages(prev => prev.slice(0, messageIndex))

      // 重新发送用户消息
      handleSendMessage(lastUserMessage.content)
    }
  }, [messages, handleSendMessage])

  // 清空对话
  const clearChat = useCallback(() => {
    setMessages([])
  }, [])

  return (
    <div className="flex flex-col h-full">
      {/* 聊天容器 */}
      <div className="flex-1 min-h-0">
        <ChatContainer
          messages={messages}
          isLoading={isLoading}
          onCopyMessage={handleCopyMessage}
          onRegenerateMessage={handleRegenerateMessage}
        />
      </div>

      {/* 输入区域 */}
      <div className="flex-shrink-0 p-4 border-t bg-card/50 backdrop-blur-sm">
        <MessageInput
          onSendMessage={handleSendMessage}
          onFileUpload={onFileUpload}
          isLoading={isLoading}
          onStopGeneration={handleStopGeneration}
          disabled={isLoading}
        />
      </div>
    </div>
  )
}
