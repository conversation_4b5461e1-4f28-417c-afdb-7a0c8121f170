import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'
import {
  Send,
  Paperclip,
  Square,
  Loader2,
} from 'lucide-react'
import { MAX_MESSAGE_LENGTH } from '@/constants'

interface MessageInputProps {
  onSendMessage: (content: string) => void
  onFileUpload?: (files: FileList) => void
  disabled?: boolean
  isLoading?: boolean
  onStopGeneration?: () => void
  placeholder?: string
}

export default function MessageInput({
  onSendMessage,
  onFileUpload,
  disabled = false,
  isLoading = false,
  onStopGeneration,
  placeholder = "输入消息...",
}: MessageInputProps) {
  const [message, setMessage] = useState('')
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 自动调整文本框高度
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      const scrollHeight = textarea.scrollHeight
      const maxHeight = 200 // 最大高度
      textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`
    }
  }

  useEffect(() => {
    adjustTextareaHeight()
  }, [message])

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  // 发送消息
  const handleSend = () => {
    const trimmedMessage = message.trim()
    if (trimmedMessage && !disabled && !isLoading) {
      onSendMessage(trimmedMessage)
      setMessage('')
      // 重置文本框高度
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto'
      }
    }
  }

  // 处理文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0 && onFileUpload) {
      onFileUpload(files)
    }
    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // 触发文件选择
  const triggerFileUpload = () => {
    fileInputRef.current?.click()
  }

  const canSend = message.trim().length > 0 && !disabled && !isLoading
  const characterCount = message.length
  const isOverLimit = characterCount > MAX_MESSAGE_LENGTH

  return (
    <Card className="p-4">
      <div className="space-y-3">
        {/* 输入区域 */}
        <div className="flex items-end space-x-3">
          {/* 文件上传按钮 */}
          <div className="flex-shrink-0">
            <Button
              variant="ghost"
              size="icon"
              onClick={triggerFileUpload}
              disabled={disabled || isLoading}
              className="h-10 w-10"
            >
              <Paperclip className="w-4 h-4" />
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".md,.txt,.sql,.json,.csv,.html,.js,.java,.py,.xml"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>

          {/* 文本输入框 */}
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              className={cn(
                "min-h-[44px] max-h-[200px] resize-none",
                isOverLimit && "border-destructive focus-visible:ring-destructive"
              )}
              rows={1}
            />
          </div>

          {/* 发送/停止按钮 */}
          <div className="flex-shrink-0">
            {isLoading ? (
              <Button
                variant="outline"
                size="icon"
                onClick={onStopGeneration}
                className="h-10 w-10 text-destructive border-destructive hover:bg-destructive/10"
              >
                <Square className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                onClick={handleSend}
                disabled={!canSend || isOverLimit}
                size="icon"
                className="h-10 w-10"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
        </div>

        {/* 底部信息 */}
        <div className="flex justify-between items-center text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span>按 Enter 发送，Shift + Enter 换行</span>
            {onFileUpload && (
              <span>支持上传文档文件</span>
            )}
          </div>
          <div className={cn(
            "font-mono",
            isOverLimit && "text-destructive font-medium"
          )}>
            {characterCount}/{MAX_MESSAGE_LENGTH}
          </div>
        </div>
      </div>
    </Card>
  )
}
