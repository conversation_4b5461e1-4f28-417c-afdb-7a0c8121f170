import { useEffect, useRef } from 'react'
import { Card } from '@/components/ui/card'
import type { Message as MessageType } from '../../types'
import Message from './Message'
import { MessageCircle } from 'lucide-react'

interface ChatContainerProps {
  messages: MessageType[]
  isLoading?: boolean
  onCopyMessage?: (content: string) => void
  onRegenerateMessage?: (messageId: string) => void
}

export default function ChatContainer({
  messages,
  isLoading = false,
  onCopyMessage,
  onRegenerateMessage,
}: ChatContainerProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 如果没有消息，显示空状态
  if (messages.length === 0) {
    return (
      <Card className="flex-1 flex items-center justify-center">
        <EmptyState />
      </Card>
    )
  }

  return (
    <Card className="flex-1 flex flex-col overflow-hidden">
      <div
        ref={containerRef}
        className="flex-1 overflow-y-auto custom-scrollbar p-4 space-y-4"
      >
        {messages.map((message) => (
          <Message
            key={message.id}
            message={message}
            onCopy={onCopyMessage}
            onRegenerate={onRegenerateMessage}
          />
        ))}

        {/* 加载指示器 */}
        {isLoading && (
          <div className="flex items-center space-x-2 text-muted-foreground">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]"></div>
              <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]"></div>
              <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
            </div>
            <span className="text-sm">AI 正在思考...</span>
          </div>
        )}

        {/* 滚动锚点 */}
        <div ref={messagesEndRef} />
      </div>
    </Card>
  )
}

function EmptyState() {
  return (
    <div className="text-center space-y-4 max-w-md mx-auto p-8">
      <div className="w-16 h-16 mx-auto rounded-full bg-primary/10 flex items-center justify-center">
        <MessageCircle className="w-8 h-8 text-primary" />
      </div>
      <div className="space-y-2">
        <h3 className="text-xl font-semibold text-foreground">
          开始与AI对话
        </h3>
        <p className="text-muted-foreground">
          选择模型和知识库标签，然后开始聊天
        </p>
      </div>
      <div className="text-sm text-muted-foreground space-y-1">
        <p>• 支持 Markdown 格式</p>
        <p>• 支持代码高亮</p>
        <p>• 支持知识库检索</p>
        <p>• 支持流式响应</p>
      </div>
    </div>
  )
}
