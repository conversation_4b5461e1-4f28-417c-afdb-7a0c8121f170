import { Button } from '@/components/ui/button'
import {
  Menu,
  Trash2,
  Download,
  MoreVertical,
  Square,
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface HeaderProps {
  sidebarOpen: boolean
  onToggleSidebar: () => void
  isMobile: boolean
  onClearChat?: () => void
  onExportChat?: () => void
  onStopGeneration?: () => void
  isGenerating?: boolean
}

export default function Header({
  sidebarOpen,
  onToggleSidebar,
  isMobile,
  onClearChat,
  onExportChat,
  onStopGeneration,
  isGenerating = false,
}: HeaderProps) {
  return (
    <header className="flex items-center justify-between p-4 border-b bg-card/50 backdrop-blur-sm">
      {/* 左侧：侧边栏切换按钮 */}
      <div className="flex items-center space-x-3">
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleSidebar}
          className={cn(
            "transition-colors",
            sidebarOpen && !isMobile && "text-primary"
          )}
        >
          <Menu className="w-5 h-5" />
        </Button>

        {/* 当前对话标题（可选） */}
        <div className="hidden sm:block">
          <h2 className="text-lg font-semibold text-foreground">
            AI 对话
          </h2>
        </div>
      </div>

      {/* 右侧：操作按钮 */}
      <div className="flex items-center space-x-2">
        {/* 停止生成按钮 */}
        {isGenerating && (
          <Button
            variant="outline"
            size="sm"
            onClick={onStopGeneration}
            className="text-destructive border-destructive hover:bg-destructive/10"
          >
            <Square className="w-4 h-4 mr-2" />
            停止生成
          </Button>
        )}

        {/* 清空对话 */}
        <Button
          variant="outline"
          size="sm"
          onClick={onClearChat}
          className="hidden sm:flex"
        >
          <Trash2 className="w-4 h-4 mr-2" />
          清空对话
        </Button>

        {/* 导出对话 */}
        <Button
          variant="outline"
          size="sm"
          onClick={onExportChat}
          className="hidden sm:flex"
        >
          <Download className="w-4 h-4 mr-2" />
          导出对话
        </Button>

        {/* 移动端更多操作菜单 */}
        <div className="sm:hidden">
          <Button variant="ghost" size="icon">
            <MoreVertical className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </header>
  )
}
