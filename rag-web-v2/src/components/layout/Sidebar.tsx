import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  MessageCircle,
  Plus,
  Upload,
  Settings,
  Moon,
  Sun,
  X,
  ChevronDown,
} from 'lucide-react'
import { useTheme } from '@/components/theme-provider'
import { useConfig } from '@/contexts/ConfigContext'
import { modelApiService, ragApiService } from '@/services'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
  isMobile: boolean
}

export default function Sidebar({ isOpen, onClose, isMobile }: SidebarProps) {
  const { theme, setTheme } = useTheme()
  const { config, updateConfig } = useConfig()
  const [models, setModels] = useState<string[]>([])
  const [tags, setTags] = useState<string[]>([])

  // 拉取模型与标签
  useEffect(() => {
    modelApiService.getModels(config.apiType).then(setModels)
    ragApiService.getRagTags().then(setTags)
  }, [config.apiType])

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  if (!isOpen) return null

  return (
    <div className="flex flex-col h-full w-80 bg-card border-r">
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
            <MessageCircle className="w-5 h-5 text-primary" />
          </div>
          <h1 className="text-lg font-semibold">AI RAG Chat</h1>
        </div>
        {isMobile && (
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        )}
      </div>

      {/* 配置区域 */}
      <div className="p-4 border-b">
        <Card className="p-4 space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              API类型
            </label>
            <select
              value={config.apiType}
              onChange={(e) => updateConfig({ apiType: e.target.value as any })}
              className="w-full p-2 text-sm border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
            >
              <option value="OLLAMA">Ollama</option>
              <option value="OPENAI">OpenAI</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              选择模型
            </label>
            <select
              value={config.selectedModel}
              onChange={(e) => updateConfig({ selectedModel: e.target.value })}
              className="w-full p-2 text-sm border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
            >
              {(models.length > 0 ? models : (config.apiType === 'OLLAMA' ? ['qwen3', 'gemma3'] : ['Qwen/Qwen2.5-7B-Instruct', 'moonshotai/Kimi-K2-Instruct'])).map(m => (
                <option key={m} value={m}>{m}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              知识库标签
            </label>
            <select
              value={config.selectedRagTag}
              onChange={(e) => updateConfig({ selectedRagTag: e.target.value })}
              className="w-full p-2 text-sm border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
            >
              <option value="">选择知识库标签</option>
              {tags.map(tag => (
                <option key={tag} value={tag}>{tag}</option>
              ))}
            </select>
          </div>
        </Card>
      </div>

      {/* 新建对话按钮 */}
      <div className="p-4 border-b">
        <Button className="w-full" size="lg">
          <Plus className="w-4 h-4 mr-2" />
          新建对话
        </Button>
      </div>

      {/* 对话历史 */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        <div className="p-4">
          <div className="space-y-2">
            <div className="p-3 rounded-lg hover:bg-accent/50 cursor-pointer transition-colors">
              <div className="font-medium text-sm truncate">
                关于React的问题
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                2分钟前
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 底部操作区域 */}
      <div className="p-4 border-t space-y-2">
        <Button variant="ghost" className="w-full justify-start" size="sm">
          <Upload className="w-4 h-4 mr-2" />
          上传知识库
        </Button>
        <Button variant="ghost" className="w-full justify-start" size="sm">
          <Settings className="w-4 h-4 mr-2" />
          设置
        </Button>
        <Button
          variant="ghost"
          className="w-full justify-start"
          size="sm"
          onClick={toggleTheme}
        >
          {theme === 'dark' ? (
            <Sun className="w-4 h-4 mr-2" />
          ) : (
            <Moon className="w-4 h-4 mr-2" />
          )}
          {theme === 'dark' ? '浅色模式' : '深色模式'}
        </Button>
      </div>
    </div>
  )
}
