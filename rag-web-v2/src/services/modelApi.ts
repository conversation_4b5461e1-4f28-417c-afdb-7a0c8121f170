import { API_CONFIG } from '../constants'
import type { ApiType } from '../types'

export class ModelApiService {
  async getModels(apiType?: ApiType): Promise<string[]> {
    // 由于后端没有统一的模型列表 API，使用配置中的模型列表
    try {
      if (apiType === 'OPENAI') {
        return API_CONFIG.MODELS.OPENAI
      } else {
        return API_CONFIG.MODELS.OLLAMA
      }
    } catch (e) {
      // 静默处理错误，返回默认模型列表
      return apiType === 'OPENAI' ? API_CONFIG.MODELS.OPENAI : API_CONFIG.MODELS.OLLAMA
    }
  }
}

export const modelApiService = new ModelApiService()
