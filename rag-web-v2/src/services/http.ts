import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { API_CONFIG } from '@/constants'
import type { ApiResponse } from '../types'

// HTTP 客户端配置
const createHttpClient = (baseURL: string = API_CONFIG.BASE_URL): AxiosInstance => {
  const client = axios.create({
    baseURL,
    timeout: 30000, // 30秒超时
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      // 添加请求时间戳
      config.metadata = { startTime: Date.now() }
      console.log(`[HTTP] ${config.method?.toUpperCase()} ${config.url}`)
      return config
    },
    (error) => {
      console.error('[HTTP] Request error:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      const duration = Date.now() - (response.config.metadata?.startTime || 0)
      console.log(`[HTTP] ${response.status} ${response.config.url} (${duration}ms)`)
      return response
    },
    (error) => {
      const duration = Date.now() - (error.config?.metadata?.startTime || 0)

      // 只在非网络连接错误时输出错误日志
      if (!error.code || error.code !== 'ERR_NETWORK') {
        console.error(`[HTTP] ${error.response?.status || 'ERROR'} ${error.config?.url} (${duration}ms)`, error.message)
      }

      // 统一错误处理
      if (error.response) {
        // 服务器响应错误
        const { status, data } = error.response
        switch (status) {
          case 400:
            throw new Error(data?.message || '请求参数错误')
          case 401:
            throw new Error('未授权访问')
          case 403:
            throw new Error('访问被拒绝')
          case 404:
            throw new Error('请求的资源不存在')
          case 500:
            throw new Error('服务器内部错误')
          default:
            throw new Error(data?.message || `请求失败 (${status})`)
        }
      } else if (error.request) {
        // 网络错误 - 静默处理，不抛出错误到控制台
        throw new Error('网络连接失败，请检查网络设置')
      } else {
        // 其他错误
        throw new Error(error.message || '请求失败')
      }
    }
  )

  return client
}

// 默认 HTTP 客户端
export const httpClient = createHttpClient()

// HTTP 服务类
export class HttpService {
  private client: AxiosInstance

  constructor(baseURL?: string) {
    this.client = createHttpClient(baseURL)
  }

  // GET 请求
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get<ApiResponse<T>>(url, config)
    return this.handleResponse(response)
  }

  // POST 请求
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post<ApiResponse<T>>(url, data, config)
    return this.handleResponse(response)
  }

  // PUT 请求
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put<ApiResponse<T>>(url, data, config)
    return this.handleResponse(response)
  }

  // DELETE 请求
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<ApiResponse<T>>(url, config)
    return this.handleResponse(response)
  }

  // 处理响应
  private handleResponse<T>(response: AxiosResponse<ApiResponse<T>>): T {
    const { data } = response

    // 检查业务状态码
    if (data.code !== '0000') {
      throw new Error(data.info || '请求失败')
    }

    return data.data
  }

  // 流式请求（用于 SSE）
  async stream(url: string, config?: AxiosRequestConfig): Promise<ReadableStream> {
    const response = await this.client.get(url, {
      ...config,
      responseType: 'stream',
      headers: {
        ...config?.headers,
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      },
    })

    return response.data
  }
}

// 默认服务实例
export const httpService = new HttpService()

// 扩展 AxiosRequestConfig 类型以支持 metadata
declare module 'axios' {
  interface AxiosRequestConfig {
    metadata?: {
      startTime: number
    }
  }
}
