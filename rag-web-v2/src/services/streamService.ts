export interface StreamOptions {
  onMessage?: (content: string) => void
  onComplete?: () => void
  onError?: (error: Error) => void
  onStart?: () => void
}

export interface StreamController {
  abort: () => void
  isActive: boolean
}

export class StreamService {
  private activeStreams = new Map<string, EventSource>()

  // 创建流式连接
  createStream(
    url: string,
    options: StreamOptions = {}
  ): StreamController {
    const streamId = this.generateStreamId()
    let isActive = true

    try {
      const eventSource = new EventSource(url)
      this.activeStreams.set(streamId, eventSource)

      // 连接开始
      eventSource.onopen = () => {
        console.log('[Stream] Connection opened:', url)
        options.onStart?.()
      }

      // 接收消息
      eventSource.onmessage = (event) => {
        try {
          // 尝试解析 JSON 数据
          const data = JSON.parse(event.data)

          // 处理不同类型的消息
          if (data.type === 'message' && data.content) {
            options.onMessage?.(data.content)
          } else if (data.type === 'complete') {
            this.closeStream(streamId)
            options.onComplete?.()
          } else if (data.type === 'error') {
            this.closeStream(streamId)
            options.onError?.(new Error(data.message || '流式响应错误'))
          } else {
            // 兼容原始格式
            if (data.choices && data.choices[0]?.message?.content) {
              options.onMessage?.(data.choices[0].message.content)
            }

            if (data.choices && data.choices[0]?.finish_reason === 'stop') {
              this.closeStream(streamId)
              options.onComplete?.()
            }
          }
        } catch (parseError) {
          // 如果不是 JSON，直接作为文本处理
          options.onMessage?.(event.data)
        }
      }

      // 连接错误
      eventSource.onerror = (error) => {
        // 只在开发环境输出详细错误信息
        if (import.meta.env.DEV) {
          console.warn('[Stream] Connection error - backend server may be unavailable')
        }
        this.closeStream(streamId)
        isActive = false
        options.onError?.(new Error('无法连接到服务器，请确保后端服务正在运行'))
      }

      // 返回控制器
      return {
        abort: () => {
          this.closeStream(streamId)
          isActive = false
        },
        get isActive() {
          return isActive && eventSource.readyState !== EventSource.CLOSED
        }
      }

    } catch (error) {
      console.error('[Stream] Create stream error:', error)
      options.onError?.(new Error('创建流式连接失败'))

      return {
        abort: () => { },
        isActive: false
      }
    }
  }

  // 关闭指定流
  private closeStream(streamId: string) {
    const eventSource = this.activeStreams.get(streamId)
    if (eventSource) {
      eventSource.close()
      this.activeStreams.delete(streamId)
      console.log('[Stream] Connection closed:', streamId)
    }
  }

  // 关闭所有流
  closeAllStreams() {
    this.activeStreams.forEach((eventSource, streamId) => {
      eventSource.close()
      console.log('[Stream] Force close:', streamId)
    })
    this.activeStreams.clear()
  }

  // 获取活跃流数量
  getActiveStreamCount(): number {
    return this.activeStreams.size
  }

  // 生成流 ID
  private generateStreamId(): string {
    return `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 流式响应解析器
export class StreamResponseParser {
  private buffer = ''

  // 解析 SSE 数据
  parseSSEData(chunk: string): Array<{ type: string; data: any }> {
    this.buffer += chunk
    const events: Array<{ type: string; data: any }> = []

    // 按行分割
    const lines = this.buffer.split('\n')
    this.buffer = lines.pop() || '' // 保留最后一行（可能不完整）

    let currentEvent: { type?: string; data?: string } = {}

    for (const line of lines) {
      const trimmedLine = line.trim()

      if (trimmedLine === '') {
        // 空行表示事件结束
        if (currentEvent.data) {
          try {
            const parsedData = JSON.parse(currentEvent.data)
            events.push({
              type: currentEvent.type || 'message',
              data: parsedData
            })
          } catch (error) {
            // 如果解析失败，作为纯文本处理
            events.push({
              type: currentEvent.type || 'message',
              data: currentEvent.data
            })
          }
        }
        currentEvent = {}
      } else if (trimmedLine.startsWith('event:')) {
        currentEvent.type = trimmedLine.substring(6).trim()
      } else if (trimmedLine.startsWith('data:')) {
        const data = trimmedLine.substring(5).trim()
        currentEvent.data = (currentEvent.data || '') + data
      }
    }

    return events
  }

  // 重置缓冲区
  reset() {
    this.buffer = ''
  }
}

// 默认服务实例
export const streamService = new StreamService()

// 清理函数，在应用卸载时调用
export const cleanupStreams = () => {
  streamService.closeAllStreams()
}
