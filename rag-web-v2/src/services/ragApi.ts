import { httpService } from './http'
import { API_CONFIG } from '@/constants'
import type { RagTag, GitRepoUpload } from '../types'

export interface FileUploadRequest {
  ragTag: string
  files: File[]
}

export interface FileUploadProgress {
  fileName: string
  progress: number
  status: 'uploading' | 'success' | 'error'
  error?: string
}

export class RagApiService {
  // 获取知识库标签列表
  async getRagTags(): Promise<string[]> {
    try {
      const response = await httpService.get<string[]>(API_CONFIG.ENDPOINTS.RAG.TAGS)
      return response || []
    } catch (error) {
      // 静默处理网络错误，避免控制台噪音
      return []
    }
  }

  // 上传文件到知识库
  async uploadFiles(
    request: FileUploadRequest,
    onProgress?: (progress: FileUploadProgress[]) => void
  ): Promise<string> {
    try {
      const formData = new FormData()
      formData.append('ragTag', request.ragTag)

      // 添加文件到 FormData
      request.files.forEach((file) => {
        formData.append('files', file)
      })

      // 初始化进度
      const progressList: FileUploadProgress[] = request.files.map(file => ({
        fileName: file.name,
        progress: 0,
        status: 'uploading',
      }))

      onProgress?.(progressList)

      const response = await httpService.post<string>(
        API_CONFIG.ENDPOINTS.RAG.UPLOAD,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)

              // 更新所有文件的进度（简化处理）
              const updatedProgress = progressList.map(item => ({
                ...item,
                progress,
                status: progress === 100 ? 'success' as const : 'uploading' as const,
              }))

              onProgress?.(updatedProgress)
            }
          },
        }
      )

      // 标记所有文件上传成功
      const finalProgress = progressList.map(item => ({
        ...item,
        progress: 100,
        status: 'success' as const,
      }))

      onProgress?.(finalProgress)

      return response
    } catch (error) {
      console.error('[RagAPI] Upload files error:', error)

      // 标记所有文件上传失败
      if (onProgress) {
        const errorProgress = request.files.map(file => ({
          fileName: file.name,
          progress: 0,
          status: 'error' as const,
          error: error instanceof Error ? error.message : '上传失败',
        }))
        onProgress(errorProgress)
      }

      throw error
    }
  }

  // 上传 Git 仓库到知识库
  async uploadGitRepo(
    request: GitRepoUpload,
    onProgress?: (status: string) => void
  ): Promise<string> {
    try {
      onProgress?.('开始克隆仓库...')

      const formData = new FormData()
      formData.append('ragTag', request.ragTag)
      formData.append('repoUrl', request.repoUrl)

      if (request.username) {
        formData.append('username', request.username)
      }

      if (request.password) {
        formData.append('password', request.password)
      }

      onProgress?.('正在处理仓库文件...')

      const response = await httpService.post<string>(
        API_CONFIG.ENDPOINTS.RAG.GIT_UPLOAD,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 120000, // 2分钟超时，Git 操作可能较慢
        }
      )

      onProgress?.('上传完成')

      return response
    } catch (error) {
      console.error('[RagAPI] Upload git repo error:', error)
      onProgress?.('上传失败')
      throw error
    }
  }

  // 验证文件类型
  validateFileType(file: File): boolean {
    const allowedExtensions = ['.md', '.txt', '.sql', '.json', '.csv', '.html', '.js', '.java', '.py', '.xml']
    const fileName = file.name.toLowerCase()
    return allowedExtensions.some(ext => fileName.endsWith(ext))
  }

  // 验证文件大小
  validateFileSize(file: File, maxSize: number = 10 * 1024 * 1024): boolean {
    return file.size <= maxSize
  }

  // 批量验证文件
  validateFiles(files: File[]): {
    valid: File[]
    invalid: Array<{ file: File; reason: string }>
  } {
    const valid: File[] = []
    const invalid: Array<{ file: File; reason: string }> = []

    files.forEach(file => {
      if (!this.validateFileType(file)) {
        invalid.push({ file, reason: '不支持的文件类型' })
      } else if (!this.validateFileSize(file)) {
        invalid.push({ file, reason: '文件大小超过限制' })
      } else {
        valid.push(file)
      }
    })

    return { valid, invalid }
  }
}

// 默认服务实例
export const ragApiService = new RagApiService()
