export interface RetryOptions {
  maxRetries: number
  delay: number
  backoff: 'linear' | 'exponential'
  retryCondition?: (error: Error) => boolean
}

export interface ErrorInfo {
  message: string
  code?: string
  type: 'network' | 'server' | 'client' | 'timeout' | 'unknown'
  retryable: boolean
}

export class ErrorHandler {
  // 默认重试配置
  private defaultRetryOptions: RetryOptions = {
    maxRetries: 3,
    delay: 1000,
    backoff: 'exponential',
    retryCondition: (error) => this.isRetryableError(error),
  }

  // 解析错误信息
  parseError(error: any): ErrorInfo {
    if (error.response) {
      // HTTP 响应错误
      const { status, data } = error.response
      return {
        message: data?.message || this.getStatusMessage(status),
        code: data?.code || status.toString(),
        type: status >= 500 ? 'server' : 'client',
        retryable: status >= 500 || status === 429, // 服务器错误或限流可重试
      }
    } else if (error.request) {
      // 网络错误
      return {
        message: '网络连接失败，请检查网络设置',
        type: 'network',
        retryable: true,
      }
    } else if (error.code === 'ECONNABORTED') {
      // 超时错误
      return {
        message: '请求超时，请稍后重试',
        type: 'timeout',
        retryable: true,
      }
    } else {
      // 其他错误
      return {
        message: error.message || '未知错误',
        type: 'unknown',
        retryable: false,
      }
    }
  }

  // 判断错误是否可重试
  private isRetryableError(error: Error): boolean {
    const errorInfo = this.parseError(error)
    return errorInfo.retryable
  }

  // 获取 HTTP 状态码对应的消息
  private getStatusMessage(status: number): string {
    const statusMessages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '访问被拒绝',
      404: '请求的资源不存在',
      429: '请求过于频繁，请稍后重试',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂时不可用',
      504: '网关超时',
    }
    
    return statusMessages[status] || `请求失败 (${status})`
  }

  // 带重试的异步函数执行
  async withRetry<T>(
    fn: () => Promise<T>,
    options: Partial<RetryOptions> = {}
  ): Promise<T> {
    const config = { ...this.defaultRetryOptions, ...options }
    let lastError: Error
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error as Error
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === config.maxRetries) {
          break
        }
        
        // 检查是否应该重试
        if (!config.retryCondition!(lastError)) {
          break
        }
        
        // 计算延迟时间
        const delay = this.calculateDelay(attempt, config.delay, config.backoff)
        
        console.warn(`[ErrorHandler] Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, lastError.message)
        
        // 等待后重试
        await this.sleep(delay)
      }
    }
    
    throw lastError
  }

  // 计算重试延迟
  private calculateDelay(attempt: number, baseDelay: number, backoff: 'linear' | 'exponential'): number {
    switch (backoff) {
      case 'linear':
        return baseDelay * (attempt + 1)
      case 'exponential':
        return baseDelay * Math.pow(2, attempt)
      default:
        return baseDelay
    }
  }

  // 睡眠函数
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 创建用户友好的错误消息
  createUserFriendlyMessage(error: any): string {
    const errorInfo = this.parseError(error)
    
    switch (errorInfo.type) {
      case 'network':
        return '网络连接失败，请检查网络设置后重试'
      case 'timeout':
        return '请求超时，请稍后重试'
      case 'server':
        return '服务器暂时不可用，请稍后重试'
      case 'client':
        return errorInfo.message
      default:
        return '操作失败，请稍后重试'
    }
  }
}

// 全局错误处理器
export class GlobalErrorHandler {
  private static instance: GlobalErrorHandler
  private errorHandler = new ErrorHandler()
  private errorListeners: Array<(error: ErrorInfo) => void> = []

  static getInstance(): GlobalErrorHandler {
    if (!GlobalErrorHandler.instance) {
      GlobalErrorHandler.instance = new GlobalErrorHandler()
    }
    return GlobalErrorHandler.instance
  }

  // 添加错误监听器
  addErrorListener(listener: (error: ErrorInfo) => void) {
    this.errorListeners.push(listener)
  }

  // 移除错误监听器
  removeErrorListener(listener: (error: ErrorInfo) => void) {
    const index = this.errorListeners.indexOf(listener)
    if (index > -1) {
      this.errorListeners.splice(index, 1)
    }
  }

  // 处理全局错误
  handleError(error: any) {
    const errorInfo = this.errorHandler.parseError(error)
    
    // 记录错误
    console.error('[GlobalError]', errorInfo)
    
    // 通知所有监听器
    this.errorListeners.forEach(listener => {
      try {
        listener(errorInfo)
      } catch (listenerError) {
        console.error('[GlobalError] Error in listener:', listenerError)
      }
    })
  }

  // 获取错误处理器实例
  getErrorHandler(): ErrorHandler {
    return this.errorHandler
  }
}

// 默认实例
export const errorHandler = new ErrorHandler()
export const globalErrorHandler = GlobalErrorHandler.getInstance()
