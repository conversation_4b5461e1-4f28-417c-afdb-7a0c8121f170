import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ThemeProvider } from '@/components/theme-provider'
import { ConfigProvider } from '@/contexts/ConfigContext'
import ChatPage from '@/pages/ChatPage'

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="rag-web-theme">
      <ConfigProvider>
        <Router>
          <div className="min-h-screen bg-background font-sans antialiased">
            <Routes>
              <Route path="/" element={<ChatPage />} />
            </Routes>
          </div>
        </Router>
      </ConfigProvider>
    </ThemeProvider>
  )
}

export default App
